using System;

namespace HRManagementSystem.Models
{
    public class Employee
    {
        public int EmployeeID { get; set; }
        public string EmployeeNumber { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public DateTime HireDate { get; set; }
        public int DepartmentID { get; set; }
        public string DepartmentName { get; set; }
        public int PositionID { get; set; }
        public string PositionName { get; set; }
        public decimal BasicSalary { get; set; }
        public int? UserID { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }

        public Employee()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        public Employee(string employeeNumber, string firstName, string lastName, 
                       string email, string phone, DateTime hireDate, 
                       int departmentID, int positionID, decimal basicSalary)
        {
            EmployeeNumber = employeeNumber;
            FirstName = firstName;
            LastName = lastName;
            Email = email;
            Phone = phone;
            HireDate = hireDate;
            DepartmentID = departmentID;
            PositionID = positionID;
            BasicSalary = basicSalary;
            IsActive = true;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{EmployeeNumber} - {FullName}";
        }
    }
}
