-- إنشاء قاعدة بيانات نظام إدارة الموارد البشرية
-- HR Management System Database Schema

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'HRManagementDB')
BEGIN
    CREATE DATABASE HRManagementDB;
END
GO

USE HRManagementDB;
GO

-- جدول المستخدمين (Users)
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    Password NVARCHAR(255) NOT NULL, -- سيتم تشفيرها
    UserType NVARCHAR(20) NOT NULL CHECK (UserType IN ('Admin', 'Employee')),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastLogin DATETIME NULL
);

-- جدول الأقسام (Departments)
CREATE TABLE Departments (
    DepartmentID INT IDENTITY(1,1) PRIMARY KEY,
    DepartmentName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    IsActive BIT DEFAULT 1
);

-- جدول الوظائف (Positions)
CREATE TABLE Positions (
    PositionID INT IDENTITY(1,1) PRIMARY KEY,
    PositionName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    IsActive BIT DEFAULT 1
);

-- جدول الموظفين (Employees)
CREATE TABLE Employees (
    EmployeeID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeNumber NVARCHAR(20) UNIQUE NOT NULL,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    FullName AS (FirstName + ' ' + LastName) PERSISTED,
    Email NVARCHAR(100) NULL,
    Phone NVARCHAR(20) NULL,
    Address NVARCHAR(200) NULL,
    DateOfBirth DATE NULL,
    HireDate DATE NOT NULL,
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID),
    PositionID INT FOREIGN KEY REFERENCES Positions(PositionID),
    BasicSalary DECIMAL(10,2) NOT NULL,
    UserID INT NULL FOREIGN KEY REFERENCES Users(UserID),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME DEFAULT GETDATE()
);

-- جدول الحضور والانصراف (Attendance)
CREATE TABLE Attendance (
    AttendanceID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT FOREIGN KEY REFERENCES Employees(EmployeeID),
    AttendanceDate DATE NOT NULL,
    ClockInTime TIME NULL,
    ClockOutTime TIME NULL,
    WorkingHours AS (
        CASE 
            WHEN ClockInTime IS NOT NULL AND ClockOutTime IS NOT NULL 
            THEN DATEDIFF(MINUTE, ClockInTime, ClockOutTime) / 60.0
            ELSE 0
        END
    ) PERSISTED,
    Status NVARCHAR(20) DEFAULT 'Present' CHECK (Status IN ('Present', 'Absent', 'Late', 'Half Day')),
    Notes NVARCHAR(500) NULL,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول أنواع الإجازات (LeaveTypes)
CREATE TABLE LeaveTypes (
    LeaveTypeID INT IDENTITY(1,1) PRIMARY KEY,
    LeaveTypeName NVARCHAR(50) NOT NULL,
    MaxDaysPerYear INT NOT NULL,
    IsActive BIT DEFAULT 1
);

-- جدول الإجازات (Leaves)
CREATE TABLE Leaves (
    LeaveID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT FOREIGN KEY REFERENCES Employees(EmployeeID),
    LeaveTypeID INT FOREIGN KEY REFERENCES LeaveTypes(LeaveTypeID),
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    TotalDays AS (DATEDIFF(DAY, StartDate, EndDate) + 1) PERSISTED,
    Reason NVARCHAR(500) NULL,
    Status NVARCHAR(20) DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Approved', 'Rejected')),
    ApprovedBy INT NULL FOREIGN KEY REFERENCES Users(UserID),
    ApprovalDate DATETIME NULL,
    RequestDate DATETIME DEFAULT GETDATE(),
    Comments NVARCHAR(500) NULL
);

-- جدول الرواتب (Salaries)
CREATE TABLE Salaries (
    SalaryID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT FOREIGN KEY REFERENCES Employees(EmployeeID),
    SalaryMonth INT NOT NULL,
    SalaryYear INT NOT NULL,
    BasicSalary DECIMAL(10,2) NOT NULL,
    Allowances DECIMAL(10,2) DEFAULT 0,
    Bonuses DECIMAL(10,2) DEFAULT 0,
    Deductions DECIMAL(10,2) DEFAULT 0,
    LatePenalty DECIMAL(10,2) DEFAULT 0,
    NetSalary AS (BasicSalary + Allowances + Bonuses - Deductions - LatePenalty) PERSISTED,
    PaymentDate DATETIME NULL,
    Status NVARCHAR(20) DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Paid', 'Cancelled')),
    CreatedDate DATETIME DEFAULT GETDATE(),
    UNIQUE(EmployeeID, SalaryMonth, SalaryYear)
);

-- إدراج البيانات الأولية

-- إدراج الأقسام
INSERT INTO Departments (DepartmentName, Description) VALUES
(N'الموارد البشرية', N'قسم إدارة الموارد البشرية'),
(N'تقنية المعلومات', N'قسم تقنية المعلومات'),
(N'المحاسبة', N'قسم المحاسبة والمالية'),
(N'التسويق', N'قسم التسويق والمبيعات'),
(N'الإدارة العامة', N'الإدارة العامة للشركة');

-- إدراج الوظائف
INSERT INTO Positions (PositionName, Description) VALUES
(N'مدير عام', N'المدير العام للشركة'),
(N'مدير قسم', N'مدير قسم'),
(N'موظف', N'موظف عادي'),
(N'محاسب', N'محاسب'),
(N'مطور برمجيات', N'مطور برمجيات'),
(N'مسوق', N'موظف تسويق');

-- إدراج أنواع الإجازات
INSERT INTO LeaveTypes (LeaveTypeName, MaxDaysPerYear) VALUES
(N'إجازة سنوية', 30),
(N'إجازة مرضية', 15),
(N'إجازة طارئة', 5),
(N'إجازة أمومة', 90),
(N'إجازة بدون راتب', 365);

-- إدراج مستخدم المدير الافتراضي
INSERT INTO Users (Username, Password, UserType) VALUES
('admin', 'admin123', 'Admin'); -- يجب تشفير كلمة المرور في التطبيق الفعلي

-- إدراج موظف تجريبي
INSERT INTO Employees (EmployeeNumber, FirstName, LastName, Email, Phone, HireDate, DepartmentID, PositionID, BasicSalary, UserID)
VALUES ('EMP001', N'أحمد', N'محمد', '<EMAIL>', '0501234567', '2024-01-01', 1, 1, 10000.00, 1);

GO
