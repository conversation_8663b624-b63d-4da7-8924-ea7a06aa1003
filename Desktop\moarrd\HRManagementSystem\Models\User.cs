using System;

namespace HRManagementSystem.Models
{
    public enum UserType
    {
        Admin,
        Employee
    }

    public class User
    {
        public int UserID { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public UserType UserType { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? LastLogin { get; set; }

        public User()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
        }

        public User(string username, string password, UserType userType)
        {
            Username = username;
            Password = password;
            UserType = userType;
            IsActive = true;
            CreatedDate = DateTime.Now;
        }

        public bool IsAdmin => UserType == UserType.Admin;
        public bool IsEmployee => UserType == UserType.Employee;

        public override string ToString()
        {
            return $"{Username} ({UserType})";
        }
    }
}
